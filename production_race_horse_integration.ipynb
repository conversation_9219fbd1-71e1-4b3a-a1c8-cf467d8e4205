# ライブラリのインポート
import pandas as pd
import numpy as np
import glob
import os
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

print("🚀 本番環境：レースデータと馬過去成績データの統合")
print("=" * 60)
print(f"実行開始時刻: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
print(f"pandas version: {pd.__version__}")
print(f"numpy version: {np.__version__}")
print("=" * 60)

# データ生成設定（必要に応じて変更）
GENERATE_DATA = True  # Trueにするとデータ生成を実行
GENERATE_RACE_DATA = True  # レースデータを生成するか
GENERATE_HORSE_DATA = True  # 馬過去成績データを生成するか
TARGET_YEARS = [2022, 2023, 2024]  # 対象年度
MAX_FILES_PER_YEAR = None  # 年度あたりの最大ファイル数（Noneで全ファイル処理）

# 馬過去成績データ生成方法の選択
HORSE_DATA_METHOD = 'all_years'  # 'by_year' または 'all_years'
# 'by_year': 年度別に処理（従来方式）
# 'all_years': 全年度一括処理（新方式、get_all_horse_results使用）

# 全年度処理時の設定
ALL_YEARS_MAX_FILES = None  # 全年度処理時の最大ファイル数（Noneで制限なし）
ALL_YEARS_RANGE = (2020, 2024)  # 全年度処理時の年度範囲

if GENERATE_DATA:
    print("\n🔧 データ生成を開始します...")
    print("-" * 50)
    
    # RaceProcessorとHorseProcessorのインポート
    try:
        from race_data_processor import RaceProcessor
        from horse_processor import HorseProcessor
        print("✅ プロセッサーのインポート完了")
    except ImportError as e:
        print(f"❌ プロセッサーのインポートエラー: {e}")
        print("   race_processor.py と horse_processor.py が必要です")
        GENERATE_DATA = False
    
    if GENERATE_DATA:
        # レースデータの生成
        print("\n📊 レースデータの生成")
        try:
            race_processor = RaceProcessor()
            
            # 年度を指定してレースデータを生成
            # 設定された年度を使用
            
            for year in TARGET_YEARS:
                print(f"   {year}年のレースデータを処理中...")
                race_data = race_processor.get_rawdata_race_results(
                    year_start=year,
                    year_end=year,
                    max_files=MAX_FILES_PER_YEAR
                )
                
                if not race_data.empty:
                    # データの保存
                    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                    filename = f"data/race_data_{year}_{timestamp}.pickle"
                    os.makedirs("data", exist_ok=True)
                    race_data.to_pickle(filename)
                    print(f"   ✅ {year}年レースデータ保存: {filename} ({len(race_data):,}件)")
                else:
                    print(f"   ⚠️ {year}年のレースデータが空です")
            
            print("✅ レースデータ生成完了")
            
        except Exception as e:
            print(f"❌ レースデータ生成エラー: {e}")
        
        # 馬過去成績データの生成
        print("\n🐎 馬過去成績データの生成")
        try:
            horse_processor = HorseProcessor()
            
            if HORSE_DATA_METHOD == 'all_years':
                # 新方式: 全年度一括処理
                print(f"   📊 全年度一括処理モード ({ALL_YEARS_RANGE[0]}-{ALL_YEARS_RANGE[1]}年)")
                print(f"   📝 最大ファイル数: {ALL_YEARS_MAX_FILES:,}件")
                
                horse_results_data = horse_processor.get_all_horse_results(
                    year_range=ALL_YEARS_RANGE,
                    max_files=ALL_YEARS_MAX_FILES
                )
                
                if not horse_results_data.empty:
                    # データの保存
                    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                    filename = f"data/horse_results_all_{ALL_YEARS_RANGE[0]}_{ALL_YEARS_RANGE[1]}_{timestamp}.pickle"
                    os.makedirs("data", exist_ok=True)
                    horse_results_data.to_pickle(filename)
                    print(f"   ✅ 全年度馬過去成績データ保存: {filename} ({len(horse_results_data):,}件)")
                    
                    # 年度別統計を表示
                    if 'data_year' in horse_results_data.columns:
                        year_stats = horse_results_data['data_year'].value_counts().sort_index()
                        print(f"   📊 年度別件数: {dict(year_stats)}")
                    elif '日付' in horse_results_data.columns:
                        # 日付から年度を抽出
                        horse_results_data['year_extracted'] = pd.to_datetime(horse_results_data['日付'], errors='coerce').dt.year
                        year_stats = horse_results_data['year_extracted'].value_counts().sort_index()
                        print(f"   📊 年度別件数: {dict(year_stats.head(10))}")
                else:
                    print(f"   ⚠️ 全年度の馬過去成績データが空です")
                    
            else:
                # 従来方式: 年度別処理
                print(f"   📅 年度別処理モード")
                
                for year in TARGET_YEARS:
                    print(f"   {year}年の馬過去成績データを処理中...")
                    horse_results_data = horse_processor.get_rawdata_horse_results(
                        year=year,
                        max_files=MAX_FILES_PER_YEAR
                    )
                    
                    if not horse_results_data.empty:
                        # データの保存
                        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                        filename = f"data/horse_results_{year}_{timestamp}.pickle"
                        horse_results_data.to_pickle(filename)
                        print(f"   ✅ {year}年馬過去成績データ保存: {filename} ({len(horse_results_data):,}件)")
                    else:
                        print(f"   ⚠️ {year}年の馬過去成績データが空です")
            
            print("✅ 馬過去成績データ生成完了")
            
        except Exception as e:
            print(f"❌ 馬過去成績データ生成エラー: {e}")
        
        print("\n🎉 データ生成処理完了")
        print("-" * 50)
        
else:
    print("\n📝 データ生成をスキップします")
    print("   データ生成が必要な場合は、GENERATE_DATA = True に変更してください")
    print("-" * 50)

print("\n📂 1. データの読み込み")
print("-" * 40)

# レース結果データの読み込み
race_files = glob.glob("data/race_data_*.pickle")
if race_files:
    latest_race_file = max(race_files)
    race_data = pd.read_pickle(latest_race_file)
    print(f"✅ レース結果データ: {latest_race_file}")
    print(f"   件数: {len(race_data):,}件, カラム: {len(race_data.columns)}個")
else:
    print("❌ レース結果データファイルが見つかりません")
    race_data = None

# 馬過去成績データの読み込み
horse_result_files = glob.glob("data/horse_results*.pickle")
if horse_result_files:
    latest_horse_file = max(horse_result_files)
    horse_results_data = pd.read_pickle(latest_horse_file)
    print(f"✅ 馬過去成績データ: {latest_horse_file}")
    print(f"   件数: {len(horse_results_data):,}件, カラム: {len(horse_results_data.columns)}個")
else:
    print("❌ 馬過去成績データファイルが見つかりません")
    horse_results_data = None

# 馬基本情報データは使用しない
print("📝 馬基本情報データ: 使用しません（Resultsデータのみ方針）")

# データ読み込み結果
if race_data is not None and horse_results_data is not None:
    print(f"\n📊 データ読み込み完了")
    print(f"   レース結果: {len(race_data):,}件")
    print(f"   馬過去成績: {len(horse_results_data):,}件")
    data_load_success = True
else:
    print("\n❌ 必要なデータが不足しています")
    data_load_success = False

print("\n🔧 2. horse_ID正規化と共通ID確認")
print("-" * 40)

if data_load_success:
    # データのコピー
    race_df = race_data.copy()
    horse_results_df = horse_results_data.copy()
    
    # horse_idの正規化
    race_df['horse_id'] = race_df['horse_id'].astype(str)
    
    if 'horse_id' not in horse_results_df.columns:
        horse_results_df['horse_id'] = horse_results_df.index.astype(str)
        print("   馬過去成績: インデックスからhorse_id作成")
    else:
        horse_results_df['horse_id'] = horse_results_df['horse_id'].astype(str)
    
    # 日付カラムの確保
    if 'date' not in horse_results_df.columns and '日付' in horse_results_df.columns:
        horse_results_df['date'] = pd.to_datetime(horse_results_df['日付'], errors='coerce')
        print("   馬過去成績: 日付カラム作成")
    
    # horse_idの一致確認
    race_horse_ids = set(race_df['horse_id'])
    horse_result_ids = set(horse_results_df['horse_id'])
    common_ids = race_horse_ids.intersection(horse_result_ids)
    
    print(f"\n📊 horse_ID分析")
    print(f"   レース結果: {len(race_horse_ids):,}個")
    print(f"   馬過去成績: {len(horse_result_ids):,}個")
    print(f"   共通ID: {len(common_ids):,}個")
    
    # 年度プレフィックス問題の修正
    if len(common_ids) == 0:
        print("\n🔧 年度プレフィックス問題を修正中...")
        race_df['horse_id_clean'] = race_df['horse_id'].str[-10:]
        race_horse_ids_clean = set(race_df['horse_id_clean'])
        common_ids_clean = race_horse_ids_clean.intersection(horse_result_ids)
        
        if len(common_ids_clean) > 0:
            race_df['horse_id'] = race_df['horse_id_clean']
            common_ids = common_ids_clean
            print(f"   ✅ 修正完了: {len(common_ids):,}個の共通ID確保")
        else:
            print("   ❌ 修正後も共通IDなし")
    
    if len(common_ids) > 0:
        print(f"\n✅ 共通horse_id: {len(common_ids):,}個")
        print(f"   サンプル: {list(common_ids)[:3]}")
        id_preparation_success = True
    else:
        print(f"\n❌ 共通horse_idが見つかりません")
        id_preparation_success = False
else:
    print("❌ データ読み込み失敗のため、ID準備をスキップ")
    id_preparation_success = False

print("\n🎯 3. 過去成績特徴量の生成")
print("-" * 40)

if id_preparation_success:
    target_cols = ['着順', '人気']
    
    # 数値カラムの確認と変換
    numeric_cols = []
    for col in target_cols:
        if col in horse_results_df.columns:
            horse_results_df[col] = pd.to_numeric(horse_results_df[col], errors='coerce')
            numeric_cols.append(col)
    
    print(f"📊 集計対象: {numeric_cols}")
    
    if numeric_cols and 'date' in horse_results_df.columns:
        # 共通IDの馬のみに絞る
        horse_results_common = horse_results_df[horse_results_df['horse_id'].isin(common_ids)].copy()
        print(f"📊 共通IDの過去成績: {len(horse_results_common):,}件")
        
        if len(horse_results_common) > 0:
            performance_stats_list = []
            
            print("\n🔧 統計計算中...")
            
            # 全期間統計
            try:
                all_stats = horse_results_common.groupby('horse_id')[numeric_cols].agg([
                    'mean', 'count', 'std'
                ]).round(3)
                all_stats.columns = [f'{col[0]}_{col[1]}_all' for col in all_stats.columns]
                performance_stats_list.append(all_stats)
                print(f"   ✅ 全期間統計: {len(all_stats):,}頭, {len(all_stats.columns)}カラム")
            except Exception as e:
                print(f"   ❌ 全期間統計エラー: {e}")
            
            # 直近レース統計
            for n_races in [3, 5, 10]:
                try:
                    recent_df = horse_results_common.sort_values(['horse_id', 'date']).groupby('horse_id').tail(n_races)
                    recent_stats = recent_df.groupby('horse_id')[numeric_cols].agg(['mean', 'count']).round(3)
                    recent_stats.columns = [f'{col[0]}_{col[1]}_{n_races}R' for col in recent_stats.columns]
                    performance_stats_list.append(recent_stats)
                    print(f"   ✅ 直近{n_races}レース: {len(recent_stats):,}頭, {len(recent_stats.columns)}カラム")
                except Exception as e:
                    print(f"   ❌ 直近{n_races}レースエラー: {e}")
            
            # 全統計を結合
            if performance_stats_list:
                try:
                    all_performance_stats = pd.concat(performance_stats_list, axis=1)
                    print(f"\n✅ 過去成績統計完了: {len(all_performance_stats):,}頭, {len(all_performance_stats.columns)}カラム")
                    feature_generation_success = True
                except Exception as e:
                    print(f"❌ 統計結合エラー: {e}")
                    feature_generation_success = False
            else:
                print("❌ 過去成績統計が生成されませんでした")
                feature_generation_success = False
        else:
            print("❌ 共通IDの過去成績データなし")
            feature_generation_success = False
    else:
        print("❌ 集計に必要なカラム不足")
        feature_generation_success = False
else:
    print("❌ ID準備失敗のため、特徴量生成をスキップ")
    feature_generation_success = False

print("\n🔗 4. データの統合")
print("-" * 40)

if feature_generation_success:
    merged_df = race_df.copy()
    print(f"📊 ベースレース結果: {len(merged_df):,}件, {len(merged_df.columns)}カラム")
    
    # 馬基本情報データは使用しない
    print("📝 馬基本情報: スキップ（Resultsデータのみ方針）")
    
    # 過去成績特徴量との結合
    try:
        print("\n🔧 過去成績特徴量結合中...")
        
        merged_df = merged_df.merge(
            all_performance_stats,
            left_on='horse_id',
            right_index=True,
            how='left'
        )
        
        print(f"✅ 結合完了: {len(merged_df):,}件, {len(merged_df.columns)}カラム")
        
        # final_merged_dataとして保存
        final_merged_data = merged_df.copy()
        integration_success = True
        
    except Exception as e:
        print(f"❌ 結合エラー: {e}")
        final_merged_data = pd.DataFrame()
        integration_success = False
else:
    print("❌ 特徴量生成失敗のため、統合をスキップ")
    final_merged_data = pd.DataFrame()
    integration_success = False

# 統合結果の確認
if not final_merged_data.empty:
    print(f"\n📊 統合結果: {len(final_merged_data):,}件, {len(final_merged_data.columns)}カラム")
else:
    print(f"\n❌ final_merged_dataが空です！")

print("\n✅ 5. 統合結果の検証")
print("-" * 40)

if not final_merged_data.empty:
    print(f"📊 最終統合データ: {len(final_merged_data):,}件")
    print(f"📊 最終カラム数: {len(final_merged_data.columns)}個")
    
    # 過去成績関連カラムの確認
    perf_cols = [col for col in final_merged_data.columns if any(x in col for x in ['_all', '_3R', '_5R', '_10R'])]
    print(f"\n🎯 過去成績関連カラム: {len(perf_cols)}個")
    
    if perf_cols:
        # 過去成績データがある出走の統計
        has_performance = final_merged_data[perf_cols].notna().any(axis=1).sum()
        success_rate = (has_performance / len(final_merged_data)) * 100
        
        print(f"\n🎯 過去成績データ統合結果")
        print(f"   過去成績データがある出走: {has_performance:,}件")
        print(f"   全出走数: {len(final_merged_data):,}件")
        print(f"   統合成功率: {success_rate:.1f}%")
        
        if has_performance > 0:
            print(f"\n📈 過去成績カラムのサンプル")
            sample_cols = perf_cols[:5]
            for col in sample_cols:
                non_null_count = final_merged_data[col].notna().sum()
                if non_null_count > 0:
                    sample_values = final_merged_data[col].dropna().head(3).tolist()
                    print(f"   {col}: {non_null_count:,}件, 例: {sample_values}")
            
            print(f"\n✅ Resultsデータのみでの過去成績データ結合成功！")
            validation_success = True
        else:
            print(f"\n❌ 過去成績データがある出走が0件")
            validation_success = False
    else:
        print(f"\n❌ 過去成績関連カラムが見つかりません")
        validation_success = False
else:
    print(f"\n❌ final_merged_dataが空です！")
    validation_success = False

final_merged_data

print("\n💾 6. データの保存")
print("-" * 40)

if not final_merged_data.empty:
    # データディレクトリの作成
    os.makedirs("data", exist_ok=True)
    
    # ファイル名の生成
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    year = datetime.now().year
    
    if validation_success:
        base_filename = f"production_integrated_race_horse_{year}_{timestamp}"
        status = "成功"
    else:
        base_filename = f"production_partial_race_horse_{year}_{timestamp}"
        status = "部分的"
    
    # Pickleファイルとして保存
    pickle_filename = f"data/{base_filename}.pickle"
    final_merged_data.to_pickle(pickle_filename)
    print(f"✅ Pickle保存: {pickle_filename}")
    
    # CSVファイルとして保存
    csv_filename = f"data/{base_filename}.csv"
    final_merged_data.to_csv(csv_filename, index=False, encoding='utf-8-sig')
    print(f"✅ CSV保存: {csv_filename}")
    
    # 保存されたデータの概要
    print(f"\n📊 保存データ概要（統合{status}）")
    print(f"   データ件数: {len(final_merged_data):,}件")
    print(f"   カラム数: {len(final_merged_data.columns)}個")
    print(f"   使用データ: Resultsデータのみ")
    
    # 過去成績関連の統計
    perf_cols = [col for col in final_merged_data.columns if any(x in col for x in ['_all', '_3R', '_5R', '_10R'])]
    if perf_cols:
        has_performance = final_merged_data[perf_cols].notna().any(axis=1).sum()
        print(f"   過去成績データがある出走: {has_performance:,}件")
        print(f"   過去成績データ割合: {(has_performance / len(final_merged_data)) * 100:.1f}%")
        print(f"   過去成績関連カラム: {len(perf_cols)}個")
    
    print(f"\n✅ データ保存完了")
else:
    print("❌ 保存するデータがありません")

print("\n" + "=" * 60)
print("🏁 本番環境：レースデータと馬過去成績データの統合 - 完了")
print("=" * 60)
print(f"完了時刻: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if not final_merged_data.empty:
    # 基本統計
    print(f"\n📊 最終結果")
    print(f"   統合データ件数: {len(final_merged_data):,}件")
    print(f"   総カラム数: {len(final_merged_data.columns)}個")
    print(f"   使用データ: Resultsデータのみ")
    
    # 過去成績関連の統計
    perf_cols = [col for col in final_merged_data.columns if any(x in col for x in ['_all', '_3R', '_5R', '_10R'])]
    if perf_cols:
        has_performance = final_merged_data[perf_cols].notna().any(axis=1).sum()
        success_rate = (has_performance / len(final_merged_data)) * 100
        
        print(f"\n🎯 過去成績データ統合結果")
        print(f"   過去成績データがある出走: {has_performance:,}件")
        print(f"   統合成功率: {success_rate:.1f}%")
        print(f"   過去成績関連特徴量: {len(perf_cols)}個")
        
        if success_rate > 0:
            print(f"\n✅ 過去成績データの結合に成功しました！")
            print(f"   この統合データは機械学習モデルの訓練に使用できます")
            print(f"   馬基本情報データを使用せず、純粋なResultsデータのみで構築")
        
        # テスト結果との比較
        print(f"\n🧪 テスト結果との比較")
        print(f"   期待値: 46,752件, 239件の過去成績データ (0.5%)")
        print(f"   実際値: {len(final_merged_data):,}件, {has_performance:,}件の過去成績データ ({success_rate:.1f}%)")
        
        if abs(len(final_merged_data) - 46752) < 100 and abs(has_performance - 239) < 50:
            print(f"   ✅ テスト結果とほぼ一致")
        else:
            print(f"   ⚠️ テスト結果と異なります")
    
    # データ構成
    print(f"\n📋 データ構成")
    print(f"   レース結果データ: ✅ 使用")
    print(f"   馬過去成績データ: ✅ 使用")
    print(f"   馬基本情報データ: ❌ 使用しない")
    
    # 生成された特徴量
    if perf_cols:
        print(f"\n🔧 生成された過去成績特徴量（上位8個）")
        for i, col in enumerate(perf_cols[:8], 1):
            non_null_count = final_merged_data[col].notna().sum()
            print(f"   {i}. {col}: {non_null_count:,}件")
        if len(perf_cols) > 8:
            print(f"   ... 他{len(perf_cols)-8}個")

else:
    print(f"\n❌ データ統合に失敗しました")
    print(f"   上記のセルでエラーが発生している可能性があります")

print(f"\n📝 次のステップ")
print(f"   1. 生成されたデータファイルの確認")
print(f"   2. 特徴量エンジニアリングの実施")
print(f"   3. 機械学習モデルの構築")
print(f"   4. 予測精度の評価")
print(f"   5. 本番運用への展開")

print("\n" + "=" * 60)
print("🎉 本番環境での統合処理が完了しました！")
print("=" * 60)

